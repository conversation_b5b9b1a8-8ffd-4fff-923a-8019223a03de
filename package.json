{"name": "@dynamic-innovative-studio/friendly-dates", "version": "2.0.0", "description": "A tiny, super fast, secure and stable, zero-dependency library that formats dates into human-friendly.", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./locales": {"types": "./dist/locales/index.d.ts", "import": "./dist/locales/index.mjs", "require": "./dist/locales/index.js"}, "./locales/*": {"types": "./dist/locales/*.d.ts", "import": "./dist/locales/*.mjs", "require": "./dist/locales/*.js"}, "./validation": {"types": "./dist/validation.d.ts", "import": "./dist/validation.mjs", "require": "./dist/validation.js"}, "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.mjs", "require": "./dist/types.js"}, "./performance": {"types": "./dist/performance.d.ts", "import": "./dist/performance.mjs", "require": "./dist/performance.js"}, "./package.json": "./package.json"}, "files": ["dist"], "publishConfig": {"access": "public", "registry": "https://npm.pkg.github.com/"}, "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:performance": "jest --testPathPattern=performance", "test:browser": "jest --testPathPattern=browser-compatibility", "lint": "eslint --ext .ts src", "lint:fix": "eslint --ext .ts src --fix", "format": "prettier --write \"src/**/*.ts\"", "type-check": "tsc --noEmit", "prepublishOnly": "npm run build"}, "keywords": ["package", "npm", "yarn", "lib", "library", "fast", "stable", "time", "format", "dates", "relative", "friendly", "human", "i18n"], "author": "Dynamic Innovative Studio", "license": "MIT", "devDependencies": {"@eslint/js": "^10.0.0", "@types/jest": "^30.0.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.32.0", "globals": "^16.3.0", "jest": "^30.0.5", "prettier": "^3.6.2", "ts-jest": "^29.4.0", "tsup": "^8.5.0", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.test.ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/__tests__/**"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "setupFilesAfterEnv": ["<rootDir>/src/__tests__/setup.ts"]}, "repository": {"type": "git", "url": "git+https://github.com/Dynamic-Innovative-Studio/friendly-dates.git"}, "bugs": {"url": "https://github.com/Dynamic-Innovative-Studio/friendly-dates/issues"}, "homepage": "https://github.com/Dynamic-Innovative-Studio/friendly-dates#readme"}