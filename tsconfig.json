{
  "compilerOptions": {
    // Target and Module Configuration
    "target": "ES2022",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "lib": ["ES2022", "DOM"],

    // Output Configuration
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "outDir": "./dist",
    "removeComments": false,
    "importHelpers": true,

    // Module Resolution
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "moduleDetection": "force",
    "allowImportingTsExtensions": false,
    "resolveJsonModule": true,

    // Type Checking - Strict Mode
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitThis": true,
    "useUnknownInCatchVariables": true,
    "alwaysStrict": true,

    // Additional Checks
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true,

    // Advanced Options
    "skipLibCheck": true,
    "allowUnusedLabels": false,
    "allowUnreachableCode": false,
    "verbatimModuleSyntax": false,

    // Emit
    "noEmitOnError": true,
    "preserveConstEnums": true,
    "declarationDir": "./dist",

    // Interop Constraints
    "isolatedModules": true,
    "allowJs": false,
    "checkJs": false
  },
  "include": [
    "src/**/*",
    "examples/"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "coverage"
  ],
  "ts-node": {
    "esm": true
  }
}
